["tests/test_agents.py::test_agent_adapt_method[ConsolidationAgent]", "tests/test_agents.py::test_agent_adapt_method[MeanReversionAgent]", "tests/test_agents.py::test_agent_adapt_method[MoEAgent]", "tests/test_agents.py::test_agent_adapt_method[PPOAgent]", "tests/test_agents.py::test_agent_adapt_method[TrendAgent]", "tests/test_agents.py::test_agent_adapt_method[VolatilityAgent]", "tests/test_agents.py::test_base_agent_abstract_methods", "tests/test_agents.py::test_gating_network_output_shape", "tests/test_agents.py::test_moe_agent_initialization", "tests/test_agents.py::test_moe_agent_learn_placeholder", "tests/test_agents.py::test_moe_agent_select_action", "tests/test_agents.py::test_ppo_agent_initialization", "tests/test_agents.py::test_ppo_agent_learn_placeholder", "tests/test_agents.py::test_ppo_agent_select_action", "tests/test_agents.py::test_specialized_agent_initialization[ConsolidationAgent]", "tests/test_agents.py::test_specialized_agent_initialization[MeanReversionAgent]", "tests/test_agents.py::test_specialized_agent_initialization[TrendAgent]", "tests/test_agents.py::test_specialized_agent_initialization[VolatilityAgent]", "tests/test_agents.py::test_specialized_agent_learn_placeholder[ConsolidationAgent]", "tests/test_agents.py::test_specialized_agent_learn_placeholder[MeanReversionAgent]", "tests/test_agents.py::test_specialized_agent_learn_placeholder[TrendAgent]", "tests/test_agents.py::test_specialized_agent_learn_placeholder[VolatilityAgent]", "tests/test_agents.py::test_specialized_agent_select_action[ConsolidationAgent]", "tests/test_agents.py::test_specialized_agent_select_action[MeanReversionAgent]", "tests/test_agents.py::test_specialized_agent_select_action[TrendAgent]", "tests/test_agents.py::test_specialized_agent_select_action[VolatilityAgent]", "tests/test_autonomous_agent.py::TestAutonomousAgent::test_autonomous_agent_act_basic", "tests/test_autonomous_agent.py::TestAutonomousAgent::test_autonomous_agent_act_with_memory", "tests/test_autonomous_agent.py::TestAutonomousAgent::test_autonomous_agent_base_agent_compatibility", "tests/test_autonomous_agent.py::TestAutonomousAgent::test_autonomous_agent_initialization", "tests/test_autonomous_agent.py::TestAutonomousAgent::test_autonomous_agent_learn_from_experience", "tests/test_autonomous_agent.py::TestAutonomousAgent::test_autonomous_agent_memory_capacity", "tests/test_autonomous_agent.py::TestAutonomousAgent::test_autonomous_agent_save_and_load", "tests/test_autonomous_agent.py::TestAutonomousAgent::test_autonomous_agent_statistics", "tests/test_autonomous_agent.py::TestAutonomousAgent::test_autonomous_agent_temperature_exploration", "tests/test_autonomous_agent.py::TestAutonomousAgent::test_autonomous_agent_think_and_predict", "tests/test_backtesting.py::TestTradingEnv::test_env_episode_done", "tests/test_backtesting.py::TestTradingEnv::test_env_initialization", "tests/test_backtesting.py::TestTradingEnv::test_env_invalid_action_penalty", "tests/test_backtesting.py::TestTradingEnv::test_env_reset", "tests/test_backtesting.py::TestTradingEnv::test_env_step_buy_long", "tests/test_backtesting.py::TestTradingEnv::test_env_step_close_long", "tests/test_backtesting.py::TestTradingEnv::test_env_step_hold", "tests/test_backtesting/test_engine.py::test_buy_long_option", "tests/test_backtesting/test_engine.py::test_buy_long_stock", "tests/test_backtesting/test_engine.py::test_close_long_option", "tests/test_backtesting/test_engine.py::test_close_long_stock", "tests/test_backtesting/test_engine.py::test_initial_capital_validation", "tests/test_backtesting/test_engine.py::test_insufficient_capital_buy", "tests/test_backtesting/test_engine.py::test_reset", "tests/test_backtesting/test_engine.py::test_trailing_stop_long_position", "tests/test_backtesting/test_engine.py::test_unrealized_pnl_option", "tests/test_backtesting/test_engine.py::test_unrealized_pnl_stock", "tests/test_config/test_instrument.py::test_instrument_creation", "tests/test_config/test_instrument.py::test_instrument_repr", "tests/test_core_transformer.py::TestCoreTransformer::test_core_transformer_attention_weights", "tests/test_core_transformer.py::TestCoreTransformer::test_core_transformer_config_methods", "tests/test_core_transformer.py::TestCoreTransformer::test_core_transformer_deterministic", "tests/test_core_transformer.py::TestCoreTransformer::test_core_transformer_different_configurations", "tests/test_core_transformer.py::TestCoreTransformer::test_core_transformer_forward_basic", "tests/test_core_transformer.py::TestCoreTransformer::test_core_transformer_gradient_flow", "tests/test_core_transformer.py::TestCoreTransformer::test_core_transformer_initialization", "tests/test_core_transformer.py::TestCoreTransformer::test_core_transformer_with_mask", "tests/test_core_transformer.py::TestCoreTransformer::test_core_transformer_without_positional_encoding", "tests/test_core_transformer.py::TestPositionalEncoding::test_positional_encoding_forward", "tests/test_core_transformer.py::TestPositionalEncoding::test_positional_encoding_initialization", "tests/test_episodic_memory.py::TestExternalMemory::test_clear_memory", "tests/test_episodic_memory.py::TestExternalMemory::test_external_memory_initialization", "tests/test_episodic_memory.py::TestExternalMemory::test_importance_decay", "tests/test_episodic_memory.py::TestExternalMemory::test_memory_capacity_and_eviction", "tests/test_episodic_memory.py::TestExternalMemory::test_memory_statistics", "tests/test_episodic_memory.py::TestExternalMemory::test_retrieve_memory_basic", "tests/test_episodic_memory.py::TestExternalMemory::test_retrieve_memory_empty", "tests/test_episodic_memory.py::TestExternalMemory::test_retrieve_memory_with_similarity_threshold", "tests/test_episodic_memory.py::TestExternalMemory::test_retrieve_with_custom_parameters", "tests/test_episodic_memory.py::TestExternalMemory::test_save_and_load_memories", "tests/test_episodic_memory.py::TestExternalMemory::test_store_memory_basic", "tests/test_episodic_memory.py::TestExternalMemory::test_store_memory_dimension_mismatch", "tests/test_episodic_memory.py::TestExternalMemory::test_store_memory_with_torch_tensor", "tests/test_episodic_memory.py::TestMemoryEvent::test_memory_event_creation", "tests/test_market_classifier.py::TestMarketClassifier::test_calculate_adx", "tests/test_market_classifier.py::TestMarketClassifier::test_calculate_atr", "tests/test_market_classifier.py::TestMarketClassifier::test_classify_market_consolidation_scenario", "tests/test_market_classifier.py::TestMarketClassifier::test_classify_market_insufficient_data", "tests/test_market_classifier.py::TestMarketClassifier::test_classify_market_trending_scenario", "tests/test_market_classifier.py::TestMarketClassifier::test_classify_market_volatile_scenario", "tests/test_market_classifier.py::TestMarketClassifier::test_classify_market_with_confidence", "tests/test_market_classifier.py::TestMarketClassifier::test_error_handling_invalid_data", "tests/test_market_classifier.py::TestMarketClassifier::test_get_market_features", "tests/test_market_classifier.py::TestMarketClassifier::test_get_market_features_insufficient_data", "tests/test_market_classifier.py::TestMarketClassifier::test_get_regime_probabilities", "tests/test_market_classifier.py::TestMarketClassifier::test_get_regime_probabilities_insufficient_data", "tests/test_market_classifier.py::TestMarketClassifier::test_market_classifier_custom_parameters", "tests/test_market_classifier.py::TestMarketClassifier::test_market_classifier_initialization", "tests/test_market_classifier.py::TestMarketClassifier::test_market_regimes_enum", "tests/test_market_classifier.py::TestMarketClassifier::test_prepare_data_dictionary", "tests/test_market_classifier.py::TestMarketClassifier::test_prepare_data_numpy_array", "tests/test_market_classifier.py::TestMarketClassifier::test_prepare_data_pandas_dataframe", "tests/test_models.py::test_actor_transformer_model", "tests/test_models.py::test_critic_transformer_model", "tests/test_models.py::test_lstm_model_multiple_layers", "tests/test_models.py::test_lstm_model_output_shape", "tests/test_models.py::test_transformer_model_multiple_layers", "tests/test_models.py::test_transformer_model_output_shape", "tests/test_nas_framework.py::TestIndividual::test_individual_creation", "tests/test_nas_framework.py::TestNASController::test_build_model_from_best", "tests/test_nas_framework.py::TestNASController::test_crossover", "tests/test_nas_framework.py::TestNASController::test_evaluate_population", "tests/test_nas_framework.py::TestNASController::test_evolve_population", "tests/test_nas_framework.py::TestNASController::test_generate_architecture", "tests/test_nas_framework.py::TestNASController::test_get_best_architecture", "tests/test_nas_framework.py::TestNASController::test_get_statistics", "tests/test_nas_framework.py::TestNASController::test_initialize_population", "tests/test_nas_framework.py::TestNASController::test_mutation", "tests/test_nas_framework.py::TestNASController::test_nas_controller_initialization", "tests/test_nas_framework.py::TestNASController::test_selection", "tests/test_nas_framework.py::TestSearchSpace::test_build_model_from_architecture", "tests/test_nas_framework.py::TestSearchSpace::test_create_layer_from_config", "tests/test_nas_framework.py::TestSearchSpace::test_get_architecture_complexity", "tests/test_nas_framework.py::TestSearchSpace::test_layer_config_creation", "tests/test_nas_framework.py::TestSearchSpace::test_layer_config_validation", "tests/test_nas_framework.py::TestSearchSpace::test_sample_layer_config", "tests/test_nas_framework.py::TestSearchSpace::test_sample_random_architecture", "tests/test_nas_framework.py::TestSearchSpace::test_search_space_initialization", "tests/test_nas_framework.py::TestSearchSpace::test_validate_architecture", "tests/test_pattern_recognizer.py::TestPattern1DCNN::test_pattern_cnn_forward_pass", "tests/test_pattern_recognizer.py::TestPattern1DCNN::test_pattern_cnn_initialization", "tests/test_pattern_recognizer.py::TestPatternDetection::test_pattern_detection_creation", "tests/test_pattern_recognizer.py::TestPatternDetection::test_pattern_detection_validation", "tests/test_pattern_recognizer.py::TestPatternRecognizer::test_calculate_technical_features", "tests/test_pattern_recognizer.py::TestPatternRecognizer::test_error_handling_invalid_data", "tests/test_pattern_recognizer.py::TestPatternRecognizer::test_get_pattern_features", "tests/test_pattern_recognizer.py::TestPatternRecognizer::test_get_pattern_features_insufficient_data", "tests/test_pattern_recognizer.py::TestPatternRecognizer::test_neural_network_integration", "tests/test_pattern_recognizer.py::TestPatternRecognizer::test_pattern_recognizer_initialization", "tests/test_pattern_recognizer.py::TestPatternRecognizer::test_pattern_recognizer_without_nn", "tests/test_pattern_recognizer.py::TestPatternRecognizer::test_pattern_types_enum", "tests/test_pattern_recognizer.py::TestPatternRecognizer::test_prepare_price_data_numpy_array", "tests/test_pattern_recognizer.py::TestPatternRecognizer::test_prepare_price_data_pandas_dataframe", "tests/test_pattern_recognizer.py::TestPatternRecognizer::test_prepare_sequence_for_nn", "tests/test_pattern_recognizer.py::TestPatternRecognizer::test_recognize_pattern_doji_scenario", "tests/test_pattern_recognizer.py::TestPatternRecognizer::test_recognize_pattern_double_top_scenario", "tests/test_pattern_recognizer.py::TestPatternRecognizer::test_recognize_pattern_hammer_scenario", "tests/test_pattern_recognizer.py::TestPatternRecognizer::test_recognize_pattern_insufficient_data", "tests/test_self_modification.py::TestModificationConfig::test_modification_config_creation", "tests/test_self_modification.py::TestModificationConfig::test_modification_config_defaults", "tests/test_self_modification.py::TestPerformanceMetrics::test_performance_metrics_creation", "tests/test_self_modification.py::TestPerformanceMetrics::test_performance_metrics_defaults", "tests/test_self_modification.py::TestSelfModificationManager::test_check_performance_and_adapt_architecture_search", "tests/test_self_modification.py::TestSelfModificationManager::test_check_performance_and_adapt_learning_rate_adaptation", "tests/test_self_modification.py::TestSelfModificationManager::test_check_performance_and_adapt_memory_consolidation", "tests/test_self_modification.py::TestSelfModificationManager::test_check_performance_and_adapt_no_modifications", "tests/test_self_modification.py::TestSelfModificationManager::test_check_performance_and_adapt_risk_adjustment", "tests/test_self_modification.py::TestSelfModificationManager::test_check_performance_and_adapt_synaptic_pruning", "tests/test_self_modification.py::TestSelfModificationManager::test_get_modification_statistics", "tests/test_self_modification.py::TestSelfModificationManager::test_performance_metrics_from_dict", "tests/test_self_modification.py::TestSelfModificationManager::test_reset_modification_state", "tests/test_self_modification.py::TestSelfModificationManager::test_self_modification_manager_initialization", "tests/test_self_modification.py::TestSelfModificationManager::test_standalone_function", "tests/test_training.py::test_comprehensive_backtesting_report", "tests/test_training.py::test_moe_training_integration", "tests/test_training.py::test_real_time_training_progress_updates", "tests/test_training.py::test_trainer_initialization", "tests/test_training.py::test_trainer_meta_train_loop", "tests/test_training.py::test_trainer_train_loop", "tests/test_utils.py::test_calculate_avg_pnl_per_trade", "tests/test_utils.py::test_calculate_max_drawdown", "tests/test_utils.py::test_calculate_num_trades", "tests/test_utils.py::test_calculate_profit_factor", "tests/test_utils.py::test_calculate_sharpe_ratio", "tests/test_utils.py::test_calculate_total_pnl", "tests/test_utils.py::test_calculate_win_rate", "tests/test_utils.py::test_get_available_tasks", "tests/test_utils.py::test_get_task_data_file_not_found", "tests/test_utils.py::test_get_task_data_success", "tests/test_utils.py::test_load_all_processed_data_corrupted_file", "tests/test_utils.py::test_load_all_processed_data_empty_dir", "tests/test_utils.py::test_load_all_processed_data_success", "tests/test_utils.py::test_load_raw_data_for_symbol_file_not_found", "tests/test_utils.py::test_load_raw_data_for_symbol_invalid_ohlc", "tests/test_utils.py::test_load_raw_data_for_symbol_success", "tests/test_utils.py::test_sample_tasks_insufficient", "tests/test_utils.py::test_sample_tasks_sufficient", "tests/test_utils/test_instrument_loader.py::test_load_instruments_file_not_found", "tests/test_utils/test_instrument_loader.py::test_load_instruments_invalid_yaml", "tests/test_utils/test_instrument_loader.py::test_load_instruments_success", "tests/test_world_model.py::TestTransformerWorldModel::test_world_model_config_methods", "tests/test_world_model.py::TestTransformerWorldModel::test_world_model_forward_basic", "tests/test_world_model.py::TestTransformerWorldModel::test_world_model_gradient_flow", "tests/test_world_model.py::TestTransformerWorldModel::test_world_model_initialization", "tests/test_world_model.py::TestTransformerWorldModel::test_world_model_interpretability", "tests/test_world_model.py::TestTransformerWorldModel::test_world_model_predict_future_states", "tests/test_world_model.py::TestTransformerWorldModel::test_world_model_simulate_action_outcomes", "tests/test_world_model.py::TestTransformerWorldModel::test_world_model_single_timestep_input", "tests/test_world_model.py::TestTransformerWorldModel::test_world_model_with_attention_weights", "tests/test_world_model.py::TestTransformerWorldModel::test_world_model_without_market_regime"]