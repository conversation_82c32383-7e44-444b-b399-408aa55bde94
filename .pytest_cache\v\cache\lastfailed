{"tests/test_training.py::test_trainer_meta_train_loop": true, "tests/test_training.py::test_real_time_training_progress_updates": true, "tests/test_training.py::test_trainer_train_loop": true, "tests/test_training.py::test_moe_training_integration": true, "tests/test_agents.py::test_specialized_agent_select_action[ConsolidationAgent]": true, "tests/test_agents.py::test_moe_agent_learn_placeholder": true, "tests/test_autonomous_agent.py::TestAutonomousAgent::test_autonomous_agent_statistics": true, "tests/test_autonomous_agent.py::TestAutonomousAgent::test_autonomous_agent_save_and_load": true, "tests/test_backtesting.py::TestTradingEnv::test_env_episode_done": true, "tests/test_backtesting.py::TestTradingEnv::test_env_initialization": true, "tests/test_backtesting.py::TestTradingEnv::test_env_invalid_action_penalty": true, "tests/test_backtesting.py::TestTradingEnv::test_env_reset": true, "tests/test_backtesting.py::TestTradingEnv::test_env_step_buy_long": true, "tests/test_backtesting.py::TestTradingEnv::test_env_step_close_long": true, "tests/test_backtesting.py::TestTradingEnv::test_env_step_hold": true, "tests/test_pattern_recognizer.py::TestPatternRecognizer::test_recognize_pattern_hammer_scenario": true, "tests/test_self_modification.py::TestSelfModificationManager::test_check_performance_and_adapt_synaptic_pruning": true, "tests/test_agents.py::test_specialized_agent_select_action[MeanReversionAgent]": true, "tests/test_agents.py::test_specialized_agent_learn_placeholder[TrendAgent]": true, "tests/test_agents.py::test_specialized_agent_learn_placeholder[MeanReversionAgent]": true, "tests/test_agents.py::test_specialized_agent_learn_placeholder[VolatilityAgent]": true, "tests/test_agents.py::test_specialized_agent_learn_placeholder[ConsolidationAgent]": true, "tests/test_agents.py::test_specialized_agent_initialization[TrendAgent]": true, "tests/test_agents.py::test_specialized_agent_initialization[MeanReversionAgent]": true, "tests/test_agents.py::test_specialized_agent_initialization[VolatilityAgent]": true, "tests/test_agents.py::test_specialized_agent_initialization[ConsolidationAgent]": true, "tests/test_agents.py::test_specialized_agent_select_action[TrendAgent]": true, "tests/test_agents.py::test_specialized_agent_select_action[VolatilityAgent]": true, "tests/test_agents.py::test_moe_agent_initialization": true, "tests/test_agents.py::test_moe_agent_select_action": true, "tests/test_agents.py::test_agent_adapt_method[TrendAgent]": true, "tests/test_agents.py::test_agent_adapt_method[MeanReversionAgent]": true, "tests/test_agents.py::test_agent_adapt_method[VolatilityAgent]": true, "tests/test_agents.py::test_agent_adapt_method[ConsolidationAgent]": true, "tests/test_agents.py::test_agent_adapt_method[MoEAgent]": true, "tests/test_autonomous_agent.py::TestAutonomousAgent::test_autonomous_agent_act_with_memory": true, "tests/test_episodic_memory.py::TestExternalMemory::test_retrieve_memory_basic": true}